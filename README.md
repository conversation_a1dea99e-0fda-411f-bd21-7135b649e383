# FIDO Utilities

A modular audio processing pipeline for voice activity detection, audio quality assessment.

## Overview

FIDO Utilities provides a flexible, node-based audio processing pipeline designed for audio analysis and preprocessing. The pipeline supports various audio processing tasks including:

- **Voice Activity Detection (VAD)** - Detect speech segments in audio
- **Audio Quality Assessment** - Evaluate audio quality using ONNX models
- **Audio Processing** - Handle audio loading, chunking, and preprocessing
- **Aggregation** - Combine and filter processed audio segments

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd fido_utilities
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up environment variables by creating a `.env` file:
```bash
vad_onnx_path = "/local/path/to/vad.onnx"
audio_quality_onnx_path = "/local/path/to/xane.onnx"
```

## Quick Start

```python
from pipeline import AudioPipeline

# Initialize the pipeline
pipeline = AudioPipeline()

# Process an audio file
output = pipeline("path/to/audio.wav")
print(output)
```

## Pipeline Nodes

### 1. Audio Processor
Handles audio loading and preprocessing:
- Loads audio files with specified sample rate
- Supports chunked processing for large files
- Converts audio to numpy arrays

**Configuration:**
```python
pipeline.update_node_config("audio_processor", {
    "sample_rate": 16000,
    "chunk_length_s": 10.0
})
```

### 2. VAD (Voice Activity Detection)
Detects speech segments in audio using ONNX models:
- Configurable threshold for speech detection
- Minimum and maximum duration constraints
- Segment filtering and concatenation

**Configuration:**
```python
pipeline.update_node_config("vad", {
    "threshold": 0.5,
    "min_duration_s": 0.1,
    "max_duration_s": 5.0
})
```

### 3. Audio Quality Checker
Evaluates audio quality using ONNX models:
- Quality scoring based on configurable thresholds
**Configuration:**
```python
pipeline.update_node_config("audio_quality_checker", {
    "quality_config": {
        "pesq_estimation": 2,
        "snr_estimation": 5,
        "stoi_estimation": 0.8
    }
})
```

### 4. Aggregator
Combines and filters processed segments:
- Filters segments based on status
- Returns final audio data arrays

## Usage Examples

### Basic Audio Processing
```python
from pipeline import AudioPipeline

pipeline = AudioPipeline()
result = pipeline("recording.wav")
```

### Enrollment Pipeline (4-second segments)
```python
pipeline = AudioPipeline()
pipeline.update_node_config("vad", {"max_duration_s": 4.015})
output = pipeline("recording.wav")
```

### Verification Pipeline (3-second segments)
```python
pipeline = AudioPipeline()
pipeline.update_node_config("vad", {"max_duration_s": 3.0})
output = pipeline("recording.wav")
```

### Debug Mode (without aggregation)
```python
pipeline = AudioPipeline()
pipeline.disable_node("aggregator")
output = pipeline("recording.wav")  # Returns all segments with metadata
pipeline.enable_node("aggregator")
```

### Chunked Processing
```python
pipeline = AudioPipeline()
pipeline.update_node_config("audio_processor", {"chunk_length_s": 10})
output = pipeline("long_audio.wav")
```

## Pipeline Control

### Enable/Disable Nodes
```python
pipeline.disable_node("audio_quality_checker")  # Skip quality checking
pipeline.enable_node("audio_quality_checker")   # Re-enable quality checking
```

### Update Node Configuration
```python
pipeline.update_node_config("vad", {
    "threshold": 0.7,
    "max_duration_s": 6.0
})
```

### Check Pipeline Status
```python
status = pipeline.get_pipeline_status()
print(status)  # {'audio_processor': True, 'vad': True, ...}
```

## Data Structures

### Segment
The core data structure representing audio segments:
```python
class Segment:
    start: float          # Start time in seconds
    end: float           # End time in seconds  
    data: np.ndarray     # Audio data
    metadata: dict       # Additional metadata
    status: Status       # PASS or FAIL
```

### Status
```python
class Status(Enum):
    PASS = "pass"
    FAIL = "fail"
```

## File Structure

```
fido_utilities/
├── pipeline/
│   ├── __init__.py
│   ├── audio_pipeline.py      # Main pipeline class
│   ├── base_node.py          # Base node classes
│   ├── config.py             # Configuration loading
│   ├── utilities.py          # Utility functions and data structures
│   └── nodes/
│       ├── audio_processor.py    # Audio loading and chunking
│       ├── vad.py                # Voice activity detection
│       ├── audio_quality_checker.py  # Audio quality assessment
│       └── aggregator.py         # Result aggregation
├── assets/
│   ├── vad.onnx              # VAD model
│   └── xane.onnx             # Audio quality model
├── examples/
│   ├── test.py               # Usage examples
│   ├── recording.wav         # Sample audio file
│   └── nist_train_102436.wav # Sample audio file
└── .env                      # Environment configuration
```

## Requirements

- Python 3.8+
- soundfile
- numpy
- soxr
- pydantic
- onnxruntime
- python-dotenv
