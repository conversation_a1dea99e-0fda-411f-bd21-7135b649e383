import soundfile as sf
import numpy as np
import soxr
from pydantic import BaseModel, ConfigDict, fields
from enum import Enum


class Status(str, Enum):
    PASS = "pass"
    FAIL = "fail"


class Segment(BaseModel):
    start: float = 0
    end: float | None = None
    data: np.ndarray | None = None
    metadata: dict = fields.Field(default_factory=dict)
    status: Status | None = None

    model_config = ConfigDict(arbitrary_types_allowed=True)


class SupportedNodes(str, Enum):
    AUDIO_PROCESSOR = "audio_processor"
    VAD = "vad"
    AUDIO_QUALITY_CHECKER = "audio_quality_checker"
    AGGREGATOR = "aggregator"


def load_audio(audio_path, tgt_sr: int = 16_000, start: int = 0, end: int = None):
    audio, orig_sr = sf.read(
        audio_path,
        start=start,
        stop=end,
        dtype="float32",
        always_2d=True,
    )
    audio = audio[:, 0]
    if end is not None and audio.shape[-1] < end - start:
        audio = np.pad(audio, (0, end - start - audio.shape[-1]))
    if orig_sr != tgt_sr:
        audio = soxr.resample(audio, orig_sr, tgt_sr)
    return audio


def chunk_audio(audio_path, tgt_sr: int = 16_000, chunk_length_s: float = None):
    audio_info = sf.info(audio_path)
    chunk_length = int(chunk_length_s * audio_info.samplerate)

    for i in range(0, audio_info.frames, chunk_length):
        yield load_audio(
            audio_path,
            tgt_sr,
            i,
            min(i + chunk_length, audio_info.frames),
        )
