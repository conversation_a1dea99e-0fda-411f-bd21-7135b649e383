from abc import ABC, abstractmethod
from typing import Any
import onnxruntime as ort
import numpy as np


class BaseNode(ABC):
    def __init__(self, name: str, enabled: bool = True, **kwargs):
        self.name = name
        self.enabled = enabled
        self.config = kwargs

    @abstractmethod
    def process(self, data: Any, **kwargs) -> Any:
        pass

    def execute(self, data: Any, **kwargs) -> Any:
        if not self.enabled:
            return data
        return self.process(data, **kwargs)


class BaseOnnxNode(BaseNode):
    def __init__(
        self,
        onnx_path: str,
        sess_options=None,
        include_output_names: list[str] = None,
        exclude_output_names: list[str] = None,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.inference_session = ort.InferenceSession(
            onnx_path, sess_options=sess_options
        )
        self.input_names = [i.name for i in self.inference_session.get_inputs()]
        self.output_names = [i.name for i in self.inference_session.get_outputs()]

        if include_output_names:
            self.output_names = [
                name for name in self.output_names if name in include_output_names
            ]
        elif exclude_output_names:
            self.output_names = [
                name for name in self.output_names if name not in exclude_output_names
            ]
        else:
            pass

    def process(self, *args) -> list[np.ndarray]:
        try:
            return self.inference_session.run(
                self.output_names,
                {name: arg for name, arg in zip(self.input_names, args)},
            )
        except Exception as e:
            print(f"Error during ONNX inference: {e}")
            for name, arg in zip(self.input_names, args):
                print(
                    f"Input {name}: {arg.shape if isinstance(arg, np.ndarray) else type(arg)}"
                )
            raise e

    def update_config(self, **kwargs):
        pass
