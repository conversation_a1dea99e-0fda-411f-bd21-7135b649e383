from pipeline.base_node import BaseNode
from pipeline import config


class AudioPipeline:
    def __init__(self):
        self.nodes: list[BaseNode] = []
        self._setup_default_pipeline()

    def __call__(self, x):
        for node in self.nodes:
            x = node.execute(x)
        return x

    def disable_node(self, node_name: str):
        is_exists = False
        for node in self.nodes:
            if node.name == node_name:
                node.enabled = False
                is_exists = True
        if not is_exists:
            print(f"Node {node_name} does not exist in the pipeline")

    def enable_node(self, node_name: str):
        is_exists = False
        for node in self.nodes:
            if node.name == node_name:
                node.enabled = True
                is_exists = True
        if not is_exists:
            print(
                f"Node {node_name} does not exist in the pipeline. Please add it first."
            )

    def disable_nodes(self, node_names: list[str]):
        """Disable multiple nodes in the pipeline."""
        for node_name in node_names:
            self.disable_node(node_name)

    def enable_nodes(self, node_names: list[str]):
        """Enable multiple nodes in the pipeline."""
        for node_name in node_names:
            self.enable_node(node_name)

    def _setup_default_pipeline(self):
        """Initialize the audio processing pipeline with default nodes."""
        from pipeline.nodes.audio_processor import AudioProcessor
        from pipeline.nodes.vad import VADNode
        from pipeline.nodes.audio_quality_checker import AudioQualityNode
        from pipeline.nodes.aggregator import AggregateNode

        self.add_node(AudioProcessor())
        self.add_node(VADNode(config.vad_onnx_path))
        self.add_node(
            AudioQualityNode(
                config.audio_quality_onnx_path,
            )
        )
        self.add_node(AggregateNode())

    def get_pipeline_status(self):
        """Get the status of all nodes in the pipeline."""
        return {node.name: node.enabled for node in self.nodes}

    def add_node(self, node: BaseNode, index: int = None):
        """Add a node to the pipeline."""
        if index is not None:
            self.nodes.insert(index, node)
        else:
            self.nodes.append(node)
        # print(f"Node <{node.name}> added to the pipeline.")

    def update_node_config(self, node_name: str, config: dict):
        """Update the configuration of a specific node."""
        for node in self.nodes:
            if node.name == node_name:
                if hasattr(node, "update_config"):
                    node.update_config(**config)
                else:
                    print(f"Node {node_name} does not support configuration updates.")
                return
        # print(f"Node {node_name} not found in the pipeline.")

    def add_nodes(self, nodes: list[BaseNode]):
        """Add multiple nodes to the pipeline."""
        for node in nodes:
            self.add_node(node)
        # print(f"{len(nodes)} nodes added to the pipeline.")
