import numpy as np
from pipeline.base_node import BaseOnnxNode
from pipeline.utilities import Segment, Status, SupportedNodes
from pydantic import BaseModel


class AudioQualityConfig(BaseModel, extra="forbid"):
    threshold_dict: dict[str, float]


class AudioQualityNode(BaseOnnxNode):
    def __init__(
        self,
        onnx_path: str,
        sess_options=None,
        include_output_names: list[str] = [
            "pesq_estimation",
            "stoi_estimation",
            "snr_estimation",
        ],
        exclude_output_names: list[str] = None,
        quality_config: dict[str, float] = {
            "pesq_estimation": 2,
            "snr_estimation": 5,
            "stoi_estimation": 0.8,
        },
    ):
        super().__init__(
            onnx_path=onnx_path,
            sess_options=sess_options,
            include_output_names=include_output_names,
            exclude_output_names=exclude_output_names,
            name=SupportedNodes.AUDIO_QUALITY_CHECKER.value,
        )
        if set(quality_config.keys()) - set(self.output_names):
            raise ValueError(
                f"Quality config keys {set(quality_config.keys()) - set(self.output_names)} "
                f"are not in the model output names {self.output_names}"
            )
        self._quality_config = AudioQualityConfig(threshold_dict=quality_config)

    def _correct_input_shape(self, input: np.ndarray):
        # Current model expects input shape to be (B, T)
        input = input.squeeze()
        if len(input.shape) == 1:
            input = input[None, :]
        elif len(input.shape) != 2:
            raise ValueError(
                f"Input data shape {input.shape} does not match model input shape (B, T)"
            )
        return input

    def update_config(self, **kwargs):
        """Update the quality thresholds."""
        try:
            self._quality_config = self._quality_config.copy(update=kwargs, deep=True)
        except Exception as e:
            print(f"Error updating quality thresholds: {e}")

    def process(self, input: list[Segment]) -> list[Segment]:
        outputs = []
        for segment in input:
            output = super().process(self._correct_input_shape(segment.data))
            scores = {k: v.item() for k, v in zip(self.output_names, output)}
            outputs.append(
                Segment(
                    start=segment.start,
                    end=segment.end,
                    data=segment.data,
                    metadata={**segment.metadata, **scores},
                    status=(Status.PASS if self._check(scores) else Status.FAIL),
                )
            )
        return outputs

    def _check(self, scores: dict) -> bool:
        """Check if the segment meets the quality threshold for a given metric."""
        return all(
            scores.get(metric, 0) >= self._quality_config.threshold_dict.get(metric, -1)
            for metric in self._quality_config.threshold_dict.keys()
        )
