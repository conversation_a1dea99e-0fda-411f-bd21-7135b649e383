import numpy as np
from pipeline.base_node import BaseOnnxNode
from pydantic import BaseModel, ValidationError
from pipeline.utilities import Segment, SupportedNodes
from itertools import groupby


class VADConfig(BaseModel, extra="forbid"):
    threshold: float = 0.5
    min_duration_s: float = 0.1
    max_duration_s: float = 5.0
    sample_rate: int = 16000
    step_size_s_per_frame: float = 0.08

    # onset: float = 0.1
    # offset: float = 0.1


class VADNode(BaseOnnxNode):
    def __init__(self, onnx_path: str, sess_options=None, **vad_config):
        super().__init__(
            onnx_path=onnx_path,
            sess_options=sess_options,
            name=SupportedNodes.VAD.value,
        )
        self._config = VADConfig(**vad_config)

    def _correct_input_shape(self, input: np.ndarray):
        # Current model expects input shape to be (B, T)
        input = input.squeeze()
        if len(input.shape) == 1:
            input = input[None, :]
        else:
            raise ValueError(
                f"Input data shape {input.shape} does not match model input shape (B, T)"
            )
        return input

    def update_config(self, **kwargs):
        """Update the VAD configuration."""
        try:
            self._config = self._config.copy(update=kwargs)
        except ValidationError as e:
            print(f"Error updating VAD config: {e}")

    def process(self, input: list[Segment]) -> list[Segment]:
        outputs = []
        for segment in input:
            output, *_ = super().process(self._correct_input_shape(segment.data))
            output = output.squeeze()
            output = self._segment_speech(
                segment.data, output, segment.start, segment.end
            )
            outputs.extend(output)
        if self._config.min_duration_s > 0:
            outputs = self._filter_short_segments(outputs)
        if self._config.max_duration_s > 0:
            outputs = self._concat_segments(outputs)
        return outputs

    def _segment_speech(
        self,
        audio: np.ndarray,
        mask: np.ndarray,
        segment_start: float = 0,
        segment_end: float = None,
    ) -> list[Segment]:
        """Segment the audio based on the VAD mask."""
        mask = (mask >= self._config.threshold).astype(int)
        mask = groupby(mask)
        outputs = []
        _start = 0
        for label, group in mask:
            group = list(group)
            length = len(group)
            _end = _start + length
            if label:
                seg_start = round(_start * self._config.step_size_s_per_frame, 3)
                seg_end = round(_end * self._config.step_size_s_per_frame, 3)
                outputs.append(
                    Segment(
                        start=seg_start + segment_start,
                        end=(
                            min(seg_end + segment_start, segment_end)
                            if segment_end is not None
                            else seg_end + segment_start
                        ),
                        data=audio[
                            int(seg_start * self._config.sample_rate) : int(
                                seg_end * self._config.sample_rate
                            )
                        ],
                    )
                )
            _start = _end
        return outputs

    def _filter_short_segments(self, segments: list[Segment]) -> list[Segment]:
        """Filter out segments that are shorter than the minimum duration."""
        return [
            seg
            for seg in segments
            if self._config.min_duration_s <= (seg.end - seg.start)
        ]

    def _concat_segments(self, segments: list[Segment]) -> list[Segment]:
        """Concatenate consecutive segments that are close to each other into longer segments.
        If a segment is longer than `max_duration_s`, it will be split into multiple segments
        """
        if not segments:
            return []
        segments.sort(key=lambda x: x.start)
        concatenated_segments: list[Segment] = []
        _current_concat_length = 0
        _current_concat_segments: list[Segment] = []

        for segment in segments:
            _current_segment_length = segment.end - segment.start
            if (
                _current_concat_length + _current_segment_length
                < self._config.max_duration_s
            ):
                _current_concat_segments.append(segment)
                _current_concat_length += _current_segment_length
            else:
                _current_cut_len_to_add = (
                    self._config.max_duration_s - _current_concat_length
                )
                _current_concat_segments.append(
                    Segment(
                        start=segment.start,
                        end=segment.start + _current_cut_len_to_add,
                        data=segment.data[
                            : int(_current_cut_len_to_add * self._config.sample_rate)
                        ],
                    )
                )
                concatenated_segments.append(
                    Segment(
                        start=_current_concat_segments[0].start,
                        end=_current_concat_segments[-1].end,
                        data=np.concatenate(
                            [seg.data for seg in _current_concat_segments]
                        ),
                        metadata={
                            "combined_from": [
                                (s.start, s.end) for s in _current_concat_segments
                            ]
                        },
                    )
                )
                segment = Segment(
                    start=segment.start + _current_cut_len_to_add,
                    end=segment.end,
                    data=segment.data[
                        int(_current_cut_len_to_add * self._config.sample_rate) :
                    ],
                )
                _current_concat_segments = [segment]
                _current_concat_length = (
                    _current_segment_length - _current_cut_len_to_add
                )
                # still larger than max_duration_s, so we need to continue
                if _current_concat_length >= self._config.max_duration_s:
                    for start in np.arange(
                        0, _current_concat_length, self._config.max_duration_s
                    ):
                        _start = segment.start + start
                        _end = _start + self._config.max_duration_s
                        if _end <= segment.end:
                            concatenated_segments.append(
                                Segment(
                                    start=_start,
                                    end=_end,
                                    data=segment.data[
                                        int(start * self._config.sample_rate) : int(
                                            (start + self._config.max_duration_s)
                                            * self._config.sample_rate
                                        )
                                    ],
                                )
                            )
                        else:
                            _current_concat_segments = [
                                Segment(
                                    start=_start,
                                    end=segment.end,
                                    data=segment.data[
                                        int(start * self._config.sample_rate) :
                                    ],
                                )
                            ]
                            _current_concat_length = segment.end - _start
                            break
        if _current_concat_segments:
            concatenated_segments.append(
                Segment(
                    start=_current_concat_segments[0].start,
                    end=_current_concat_segments[-1].end,
                    data=np.concatenate([seg.data for seg in _current_concat_segments]),
                    metadata={
                        "combined_from": [
                            (s.start, s.end) for s in _current_concat_segments
                        ]
                    },
                )
            )
        return concatenated_segments
