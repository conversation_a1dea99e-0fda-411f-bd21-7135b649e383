import numpy as np
from pydantic import BaseModel
import soundfile as sf

from pipeline.base_node import BaseNode
from pipeline.utilities import Segment, SupportedNodes, chunk_audio, load_audio


class AudioProcessorConfig(BaseModel):
    sample_rate: int = 16000
    channels: int = 1
    chunk_length_s: float = 10.0


class AudioProcessor(BaseNode):
    def __init__(self, **kwargs):
        super().__init__(name=SupportedNodes.AUDIO_PROCESSOR.value)
        self._config = AudioProcessorConfig(**kwargs)

    def update_config(self, **kwargs):
        """Update the audio processor configuration."""
        try:
            self._config = self._config.copy(update=kwargs)
        except Exception as e:
            print(f"Error updating audio processor config: {e}")

    def process(self, input):
        if isinstance(input, str):
            if self._config.chunk_length_s is None:
                return [
                    Segment(
                        data=load_audio(
                            input,
                            self._config.sample_rate,
                        ),
                    )
                ]
            else:
                audio_duration = sf.info(input).duration
                return [
                    Segment(
                        start=self._config.chunk_length_s * i,
                        end=min(self._config.chunk_length_s * (i + 1), audio_duration),
                        data=chunk,
                    )
                    for i, chunk in enumerate(
                        chunk_audio(
                            input, self._config.sample_rate, self._config.chunk_length_s
                        )
                    )
                ]
        elif isinstance(input, np.ndarray):
            return [Segment(data=input)]
        else:
            raise ValueError("Input must be a file path or a numpy array")
