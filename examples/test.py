if __name__ == "__main__":
    import sys

    sys.path.append("../")
    from pipeline import AudioPipeline

    pipeline = AudioPipeline()

    # enrollment pipeline, full audio return final chunk audio
    pipeline.update_node_config("vad", {"max_duration_s": 4.015})
    output = pipeline("./recording.wav")
    print(output)

    print("=" * 10)

    # enrollment pipeline, full audio return all chunks of audio include score for debug
    pipeline.disable_node("aggregator")
    output = pipeline("./recording.wav")
    pipeline.enable_node("aggregator")
    print(output)

    print("=" * 10)

    # verify pipeline, full audio return filnal chunk audio
    pipeline.update_node_config("vad", {"max_duration_s": 3})
    output = pipeline("./recording.wav")
    print(output)

    print("=" * 10)

    # enrollment pipeline, chunked audio return all chunks of audio include score for debug
    pipeline.update_node_config("audio_processor", {"chunk_length_s": 10})
    pipeline.update_node_config("vad", {"max_duration_s": 4.015})
    pipeline.disable_node("aggregator")
    output = pipeline("./nist_train_102436.wav")
    print(output)
